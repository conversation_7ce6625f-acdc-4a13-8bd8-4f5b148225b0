{"rules": [{"id": "unified_function_001", "name": "线路保护功能完整性检查", "description": "线路保护装置必须配置距离保护(PDIS)和过流保护(PTOC)功能", "category": "UNIFIED_FUNCTION", "severity": "CRITICAL", "status": "ACTIVE", "conditions": [{"field": "ieds", "operator": "exists", "value": true, "description": "必须存在IED配置"}], "tags": ["protection", "line", "function"]}, {"id": "unified_function_002", "name": "主变保护功能完整性检查", "description": "主变保护装置必须配置差动保护(PDIF)功能", "category": "UNIFIED_FUNCTION", "severity": "CRITICAL", "status": "ACTIVE", "conditions": [{"field": "ieds", "operator": "exists", "value": true, "description": "必须存在IED配置"}], "tags": ["protection", "transformer", "function"]}, {"id": "unified_terminal_001", "name": "虚端子命名规范检查", "description": "虚端子命名必须符合格式：(SV|GOOSE)_(V|I|TRIP|SIGN)_[间隔]_[序号]", "category": "UNIFIED_TERMINAL", "severity": "MAJOR", "status": "ACTIVE", "conditions": [{"field": "virtual_terminals", "operator": "exists", "value": true, "description": "必须存在虚端子配置"}], "tags": ["naming", "virtual_terminal"]}, {"id": "unified_terminal_002", "name": "SV连接完整性检查", "description": "保护装置必须正确接收本间隔的电流电压SV信号", "category": "UNIFIED_TERMINAL", "severity": "CRITICAL", "status": "ACTIVE", "conditions": [{"field": "communication.sub_networks", "operator": "exists", "value": true, "description": "必须存在通信配置"}], "tags": ["sv", "connection", "protection"]}, {"id": "unified_terminal_003", "name": "GOOSE连接完整性检查", "description": "跳闸和信号GOOSE连接必须正确配置", "category": "UNIFIED_TERMINAL", "severity": "CRITICAL", "status": "ACTIVE", "conditions": [{"field": "communication.sub_networks", "operator": "exists", "value": true, "description": "必须存在通信配置"}], "tags": ["goose", "connection", "trip"]}, {"id": "unified_interface_001", "name": "MAC地址唯一性检查", "description": "每个IED的MAC地址必须唯一，不能重复", "category": "UNIFIED_INTERFACE", "severity": "CRITICAL", "status": "ACTIVE", "conditions": [{"field": "communication.sub_networks", "operator": "exists", "value": true, "description": "必须存在通信配置"}], "tags": ["mac", "address", "unique"]}, {"id": "unified_interface_002", "name": "APPID分配规范检查", "description": "GOOSE和SV的APPID分配必须符合统一规范，避免冲突", "category": "UNIFIED_INTERFACE", "severity": "MAJOR", "status": "ACTIVE", "conditions": [{"field": "communication.sub_networks", "operator": "exists", "value": true, "description": "必须存在通信配置"}], "tags": ["appid", "allocation", "goose", "sv"]}, {"id": "unified_interface_003", "name": "MAC地址格式检查", "description": "MAC地址格式必须符合标准格式：XX:XX:XX:XX:XX:XX", "category": "UNIFIED_INTERFACE", "severity": "MAJOR", "status": "ACTIVE", "conditions": [{"field": "communication.sub_networks", "operator": "exists", "value": true, "description": "必须存在通信配置"}], "tags": ["mac", "format", "standard"]}, {"id": "unified_setting_001", "name": "定值项命名规范检查", "description": "定值项命名必须符合统一规范", "category": "UNIFIED_SETTING", "severity": "MINOR", "status": "ACTIVE", "conditions": [{"field": "data_type_templates", "operator": "exists", "value": true, "description": "必须存在数据类型模板"}], "tags": ["setting", "naming"]}, {"id": "unified_setting_002", "name": "定值单位规范检查", "description": "定值单位必须使用标准单位（A、V、MW等）", "category": "UNIFIED_SETTING", "severity": "MINOR", "status": "ACTIVE", "conditions": [{"field": "data_type_templates", "operator": "exists", "value": true, "description": "必须存在数据类型模板"}], "tags": ["setting", "unit", "standard"]}, {"id": "unified_device_id_001", "name": "保护装置命名规范检查", "description": "保护装置命名必须符合格式：R[设备号]P[保护套数]，如R1P1", "category": "UNIFIED_DEVICE_ID", "severity": "MAJOR", "status": "ACTIVE", "conditions": [{"field": "ieds", "operator": "exists", "value": true, "description": "必须存在IED配置"}], "tags": ["naming", "protection", "device"]}, {"id": "unified_device_id_002", "name": "合并单元命名规范检查", "description": "合并单元命名必须符合格式：MU[间隔号]，如MU01", "category": "UNIFIED_DEVICE_ID", "severity": "MAJOR", "status": "ACTIVE", "conditions": [{"field": "ieds", "operator": "exists", "value": true, "description": "必须存在IED配置"}], "tags": ["naming", "merging_unit", "device"]}, {"id": "unified_device_id_003", "name": "智能终端命名规范检查", "description": "智能终端命名必须符合格式：IT[间隔号]，如IT01", "category": "UNIFIED_DEVICE_ID", "severity": "MAJOR", "status": "ACTIVE", "conditions": [{"field": "ieds", "operator": "exists", "value": true, "description": "必须存在IED配置"}], "tags": ["naming", "intelligent_terminal", "device"]}, {"id": "unified_standard_001", "name": "ICD版本一致性检查", "description": "同类型同厂家装置必须使用统一版本的ICD文件", "category": "UNIFIED_STANDARD", "severity": "MAJOR", "status": "ACTIVE", "conditions": [{"field": "ieds", "operator": "exists", "value": true, "description": "必须存在IED配置"}], "tags": ["icd", "version", "consistency"]}, {"id": "unified_standard_002", "name": "装置能力描述规范检查", "description": "装置能力描述必须符合IEC 61850标准格式", "category": "UNIFIED_STANDARD", "severity": "MINOR", "status": "ACTIVE", "conditions": [{"field": "data_type_templates", "operator": "exists", "value": true, "description": "必须存在数据类型模板"}], "tags": ["capability", "description", "standard"]}], "templates": [{"name": "保护功能检查模板_{protection_type}", "description": "{protection_type}保护装置必须配置{required_functions}功能", "category": "UNIFIED_FUNCTION", "severity": "CRITICAL", "conditions": [{"field": "ieds", "operator": "exists", "value": true, "description": "必须存在IED配置"}], "parameters": {"protection_type": "线路", "required_functions": "距离保护和过流保护"}}, {"name": "设备命名检查模板_{device_type}", "description": "{device_type}设备命名必须符合格式：{naming_pattern}", "category": "UNIFIED_DEVICE_ID", "severity": "MAJOR", "conditions": [{"field": "ieds", "operator": "exists", "value": true, "description": "必须存在IED配置"}], "parameters": {"device_type": "保护装置", "naming_pattern": "R[设备号]P[保护套数]"}}]}