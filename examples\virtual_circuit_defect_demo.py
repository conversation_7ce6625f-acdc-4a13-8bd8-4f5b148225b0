"""
虚端子回路缺陷检测演示

专门演示虚端子构成回路的缺陷检测功能，
这是系统最重要的核心功能。
"""

import sys
import logging
import json
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    from smart_substation_validator.circuit_analysis.circuit_analyzer import CircuitAnalyzer
    from smart_substation_validator.circuit_analysis.defect_detector import VirtualCircuitDefectDetector
    from smart_substation_validator.circuit_analysis.circuit_models import (
        VirtualCircuit, CircuitNode, CircuitConnection, DefectType, DefectSeverity
    )
    from smart_substation_validator.iec61850.data_models import (
        SCDModel, IED, IEDType, VirtualTerminal, SignalType
    )
    print("✓ 成功导入虚端子回路缺陷检测模块")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_demo_scd_with_defects() -> SCDModel:
    """创建包含各种缺陷的演示SCD模型"""
    print("创建包含缺陷的演示SCD模型...")
    
    scd_model = SCDModel()
    scd_model.header = {
        "id": "缺陷检测演示工程",
        "version": "1.0",
        "description": "包含各种虚端子回路缺陷的演示模型"
    }
    
    # 1. 创建正常的保护装置
    protection_ied = IED(
        name="R1P1",
        type=IEDType.PROTECTION,
        manufacturer="演示厂商",
        config_version="1.0",
        description="110kV线路保护装置"
    )
    scd_model.add_ied(protection_ied)
    
    # 2. 创建合并单元
    merging_unit = IED(
        name="MU01",
        type=IEDType.MERGING_UNIT,
        manufacturer="演示厂商",
        config_version="1.0",
        description="110kV线路合并单元"
    )
    scd_model.add_ied(merging_unit)
    
    # 3. 创建智能终端
    intelligent_terminal = IED(
        name="IT01",
        type=IEDType.INTELLIGENT_TERMINAL,
        manufacturer="演示厂商",
        config_version="1.0",
        description="110kV线路智能终端"
    )
    scd_model.add_ied(intelligent_terminal)
    
    # 4. 创建有缺陷的保护装置（缺少输入信号）
    defective_protection = IED(
        name="R2P1",
        type=IEDType.PROTECTION,
        manufacturer="演示厂商",
        config_version="1.0",
        description="有缺陷的保护装置"
    )
    scd_model.add_ied(defective_protection)
    
    # 5. 创建孤立的测量装置（没有连接）
    isolated_measurement = IED(
        name="M01",
        type=IEDType.OTHER,
        manufacturer="演示厂商",
        config_version="1.0",
        description="孤立的测量装置"
    )
    scd_model.add_ied(isolated_measurement)
    
    # 创建虚端子连接
    
    # 正常连接：合并单元到保护装置的电流信号
    sv_current_connection = VirtualTerminal(
        name="SV_I_A_MU01_R1P1",
        signal_type=SignalType.SV,
        source_ied="MU01",
        source_ln="TCTR1",
        source_do="Amp",
        dest_ied="R1P1",
        dest_ln="PTOC1",
        dest_do="A",
        description="A相电流SV信号"
    )
    scd_model.add_virtual_terminal(sv_current_connection)
    
    # 正常连接：保护装置到智能终端的跳闸信号
    goose_trip_connection = VirtualTerminal(
        name="GOOSE_TRIP_R1P1_IT01",
        signal_type=SignalType.GOOSE,
        source_ied="R1P1",
        source_ln="PTOC1",
        source_do="Tr",
        dest_ied="IT01",
        dest_ln="CSWI1",
        dest_do="Pos",
        description="跳闸GOOSE信号"
    )
    scd_model.add_virtual_terminal(goose_trip_connection)
    
    # 缺陷1：错误的信号类型匹配 - 电流信号连接到电压输入
    wrong_signal_connection = VirtualTerminal(
        name="SV_WRONG_MU01_R2P1",
        signal_type=SignalType.SV,
        source_ied="MU01",
        source_ln="TCTR1",  # 电流互感器
        source_do="Amp",
        dest_ied="R2P1",
        dest_ln="TVTR1",    # 错误：连接到电压互感器输入
        dest_do="Vol",
        description="错误的信号连接"
    )
    scd_model.add_virtual_terminal(wrong_signal_connection)
    
    # 缺陷2：保护装置没有跳闸输出连接（R2P1缺少输出）
    
    # 缺陷3：重复连接
    duplicate_connection = VirtualTerminal(
        name="SV_I_A_MU01_R1P1_DUPLICATE",
        signal_type=SignalType.SV,
        source_ied="MU01",
        source_ln="TCTR1",
        source_do="Amp",
        dest_ied="R1P1",
        dest_ln="PTOC1",
        dest_do="A",
        description="重复的A相电流SV信号"
    )
    scd_model.add_virtual_terminal(duplicate_connection)
    
    # 缺陷4：GOOSE信号连接到错误的目标
    wrong_goose_connection = VirtualTerminal(
        name="GOOSE_WRONG_R1P1_MU01",
        signal_type=SignalType.GOOSE,
        source_ied="R1P1",
        source_ln="PTOC1",
        source_do="Tr",
        dest_ied="MU01",    # 错误：跳闸信号连接到合并单元
        dest_ln="TCTR1",
        dest_do="Amp",
        description="错误的GOOSE连接"
    )
    scd_model.add_virtual_terminal(wrong_goose_connection)
    
    print(f"✓ 创建了包含{len(scd_model.ieds)}个IED和{len(scd_model.virtual_terminals)}个虚端子的缺陷演示模型")
    return scd_model


def demo_circuit_analysis():
    """演示回路分析功能"""
    print("\n=== 虚端子回路分析演示 ===")
    
    # 创建演示SCD模型
    scd_model = create_demo_scd_with_defects()
    
    # 创建回路分析器
    circuit_analyzer = CircuitAnalyzer()
    
    # 分析虚端子回路
    print("分析虚端子回路...")
    analysis_result = circuit_analyzer.analyze_circuits(scd_model)
    
    # 显示分析结果
    print(f"✓ 回路分析完成")
    print(f"  识别回路数: {len(analysis_result.circuits)}")
    print(f"  分析耗时: {analysis_result.analysis_time:.3f}秒")
    
    # 显示回路详情
    print("\n识别的回路:")
    for i, circuit in enumerate(analysis_result.circuits, 1):
        print(f"  {i}. {circuit.name} ({circuit.circuit_type})")
        print(f"     节点数: {len(circuit.nodes)}")
        print(f"     连接数: {len(circuit.connections)}")
        print(f"     完整性: {'✓' if circuit.is_complete() else '✗'}")
    
    # 显示统计信息
    print(f"\n统计信息:")
    for key, value in analysis_result.statistics.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value}")
    
    return analysis_result


def demo_defect_detection(analysis_result):
    """演示缺陷检测功能"""
    print("\n=== 虚端子回路缺陷检测演示 ===")
    
    # 创建缺陷检测器
    defect_detector = VirtualCircuitDefectDetector()
    
    # 检测缺陷
    print("检测虚端子回路缺陷...")
    result_with_defects = defect_detector.detect_defects(analysis_result)
    
    # 显示检测结果摘要
    summary = result_with_defects.get_summary()
    print(f"✓ 缺陷检测完成")
    print(f"  发现缺陷总数: {summary['total_defects']}")
    print(f"  严重缺陷: {summary['critical_defects']}")
    print(f"  重要缺陷: {summary['major_defects']}")
    print(f"  轻微缺陷: {summary['minor_defects']}")
    print(f"  警告: {summary['warnings']}")
    
    # 按缺陷类型分类显示
    print(f"\n按缺陷类型统计:")
    for defect_type, count in summary['defect_types'].items():
        print(f"  {defect_type}: {count}")
    
    # 显示详细缺陷信息
    print(f"\n详细缺陷信息:")
    
    # 按严重程度排序显示
    critical_defects = result_with_defects.get_critical_defects()
    major_defects = result_with_defects.get_defects_by_severity(DefectSeverity.MAJOR)
    minor_defects = result_with_defects.get_defects_by_severity(DefectSeverity.MINOR)
    warnings = result_with_defects.get_defects_by_severity(DefectSeverity.WARNING)
    
    all_defects = critical_defects + major_defects + minor_defects + warnings
    
    for i, defect in enumerate(all_defects, 1):
        print(f"\n{i}. 【{defect.severity.value}】{defect.defect_type.value}")
        print(f"   描述: {defect.description}")
        print(f"   位置: {defect.location}")
        print(f"   建议: {defect.suggested_fix}")
        
        if defect.details:
            print(f"   详情: {defect.details}")
        
        if defect.affected_circuit:
            print(f"   影响回路: {defect.affected_circuit}")
    
    return result_with_defects


def demo_defect_report_generation(result_with_defects):
    """演示缺陷报告生成"""
    print("\n=== 缺陷报告生成演示 ===")
    
    # 创建输出目录
    output_dir = Path("output/circuit_defect_reports")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成详细的JSON报告
    report_data = {
        "summary": result_with_defects.get_summary(),
        "circuits": [
            {
                "id": circuit.id,
                "name": circuit.name,
                "type": circuit.circuit_type,
                "description": circuit.description,
                "node_count": len(circuit.nodes),
                "connection_count": len(circuit.connections),
                "is_complete": circuit.is_complete(),
                "nodes": [
                    {
                        "id": node.id,
                        "reference": node.full_reference,
                        "type": node.node_type,
                        "signal_type": node.signal_type,
                        "properties": node.properties
                    }
                    for node in circuit.nodes
                ],
                "connections": [
                    {
                        "id": conn.id,
                        "source": conn.source_node.full_reference,
                        "target": conn.target_node.full_reference,
                        "signal_type": conn.signal_type,
                        "connection_type": conn.connection_type,
                        "properties": conn.properties
                    }
                    for conn in circuit.connections
                ]
            }
            for circuit in result_with_defects.circuits
        ],
        "defects": [defect.to_dict() for defect in result_with_defects.defects],
        "recommendations": generate_recommendations(result_with_defects)
    }
    
    # 保存JSON报告
    json_report_file = output_dir / "virtual_circuit_defects.json"
    with open(json_report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"✓ 生成JSON报告: {json_report_file}")
    
    # 生成HTML报告
    html_report = generate_html_report(report_data)
    html_report_file = output_dir / "virtual_circuit_defects.html"
    with open(html_report_file, 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print(f"✓ 生成HTML报告: {html_report_file}")
    
    return output_dir


def generate_recommendations(result_with_defects):
    """生成修复建议"""
    recommendations = []
    
    # 按缺陷类型统计
    defect_counts = {}
    for defect in result_with_defects.defects:
        defect_type = defect.defect_type
        defect_counts[defect_type] = defect_counts.get(defect_type, 0) + 1
    
    # 生成针对性建议
    if DefectType.MISSING_SOURCE in defect_counts:
        recommendations.append("发现回路缺少信号源，建议检查合并单元(MU)和互感器的SV信号配置")
    
    if DefectType.MISSING_DESTINATION in defect_counts:
        recommendations.append("发现回路缺少信号目标，建议检查保护装置和控制装置的信号接收配置")
    
    if DefectType.SIGNAL_TYPE_MISMATCH in defect_counts:
        recommendations.append("发现信号类型不匹配，建议核对IEC 61850逻辑节点的信号兼容性")
    
    if DefectType.PROTECTION_LOGIC_ERROR in defect_counts:
        recommendations.append("发现保护逻辑错误，建议检查保护装置与开关控制装置的连接关系")
    
    if DefectType.INCOMPLETE_CIRCUIT in defect_counts:
        recommendations.append("发现回路不完整，建议补全缺失的虚端子连接")
    
    if DefectType.BACKUP_PROTECTION_MISSING in defect_counts:
        recommendations.append("发现缺少后备保护，建议为主保护配置相应的后备保护装置")
    
    return recommendations


def generate_html_report(report_data):
    """生成HTML报告"""
    html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚端子回路缺陷检测报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .summary {{ background: #e8f4fd; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .defect {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
        .critical {{ border-left: 5px solid #dc3545; }}
        .major {{ border-left: 5px solid #fd7e14; }}
        .minor {{ border-left: 5px solid #ffc107; }}
        .warning {{ border-left: 5px solid #20c997; }}
        .recommendations {{ background: #d4edda; padding: 15px; border-radius: 5px; margin-top: 20px; }}
        table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>虚端子回路缺陷检测报告</h1>
        <p>生成时间: {report_data['summary'].get('analysis_time', 'N/A')}秒</p>
    </div>
    
    <div class="summary">
        <h2>检测摘要</h2>
        <table>
            <tr><th>项目</th><th>数量</th></tr>
            <tr><td>回路总数</td><td>{report_data['summary']['total_circuits']}</td></tr>
            <tr><td>缺陷总数</td><td>{report_data['summary']['total_defects']}</td></tr>
            <tr><td>严重缺陷</td><td>{report_data['summary']['critical_defects']}</td></tr>
            <tr><td>重要缺陷</td><td>{report_data['summary']['major_defects']}</td></tr>
            <tr><td>轻微缺陷</td><td>{report_data['summary']['minor_defects']}</td></tr>
            <tr><td>警告</td><td>{report_data['summary']['warnings']}</td></tr>
        </table>
    </div>
    
    <h2>详细缺陷列表</h2>
"""
    
    # 添加缺陷详情
    for i, defect in enumerate(report_data['defects'], 1):
        severity_class = defect['severity'].lower()
        html_template += f"""
    <div class="defect {severity_class}">
        <h3>{i}. 【{defect['severity']}】{defect['defect_type']}</h3>
        <p><strong>描述:</strong> {defect['description']}</p>
        <p><strong>位置:</strong> {defect['location']}</p>
        <p><strong>建议:</strong> {defect['suggested_fix']}</p>
    </div>
"""
    
    # 添加修复建议
    html_template += """
    <div class="recommendations">
        <h2>修复建议</h2>
        <ul>
"""
    
    for recommendation in report_data['recommendations']:
        html_template += f"            <li>{recommendation}</li>\n"
    
    html_template += """
        </ul>
    </div>
</body>
</html>"""
    
    return html_template


def main():
    """主演示函数"""
    print("虚端子回路缺陷检测系统 - 专项演示")
    print("=" * 60)
    
    try:
        # 1. 回路分析演示
        analysis_result = demo_circuit_analysis()
        
        # 2. 缺陷检测演示
        result_with_defects = demo_defect_detection(analysis_result)
        
        # 3. 报告生成演示
        output_dir = demo_defect_report_generation(result_with_defects)
        
        print("\n" + "=" * 60)
        print("✓ 虚端子回路缺陷检测演示完成!")
        
        print(f"\n核心功能验证:")
        print(f"✓ 回路分析: 识别出{len(analysis_result.circuits)}个虚端子回路")
        print(f"✓ 缺陷检测: 发现{len(result_with_defects.defects)}个缺陷")
        print(f"✓ 报告生成: 输出到 {output_dir}")
        
        print(f"\n检测到的主要缺陷类型:")
        summary = result_with_defects.get_summary()
        for defect_type, count in summary['defect_types'].items():
            print(f"  - {defect_type}: {count}个")
        
        print(f"\n输出文件:")
        print(f"  - JSON报告: {output_dir}/virtual_circuit_defects.json")
        print(f"  - HTML报告: {output_dir}/virtual_circuit_defects.html")
        
        print(f"\n系统价值:")
        print(f"  ✓ 自动识别虚端子回路构成")
        print(f"  ✓ 智能检测回路完整性缺陷")
        print(f"  ✓ 发现信号类型匹配错误")
        print(f"  ✓ 检查保护逻辑一致性")
        print(f"  ✓ 提供具体修复建议")
        
    except Exception as e:
        print(f"\n✗ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
